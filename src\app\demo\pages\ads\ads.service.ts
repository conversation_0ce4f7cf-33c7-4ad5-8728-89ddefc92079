import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';

export interface Ad {
  id: number;
  image?: string;
  title: string;
  description: string;
  expireAt: Date;
  isExpired: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class AdsService {
  private apiUrl =  environment.URL + '/ads';

  constructor(private http: HttpClient) { }

  private getHeaders(): HttpHeaders {
    const token = localStorage.getItem('token'); // Or wherever you store your token
    return new HttpHeaders().set('Authorization', `Bearer ${token}`);
  }

  getAds(page: number, limit: number): Observable<{ data: Ad[], total: number }> {
    return this.http.get<{ data: Ad[], total: number }>(`${this.apiUrl}?page=${page}&limit=${limit}`, { headers: this.getHeaders() });
  }

  createAd(adData: FormData): Observable<Ad> {
    return this.http.post<Ad>(this.apiUrl, adData, { headers: this.getHeaders() });
  }

  updateAd(id: number, adData: FormData): Observable<Ad> {
    return this.http.put<Ad>(`${this.apiUrl}/${id}`, adData, { headers: this.getHeaders() });
  }

  deleteAd(id: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${id}`, { headers: this.getHeaders() });
  }
}
