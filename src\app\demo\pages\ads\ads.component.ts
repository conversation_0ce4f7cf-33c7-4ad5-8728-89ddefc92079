import { Component, OnInit } from '@angular/core';
import { SharedModule } from 'src/app/theme/shared/shared.module';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { CommonModule } from '@angular/common';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzModalModule, NzModalService } from 'ng-zorro-antd/modal';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzUploadFile, NzUploadModule } from 'ng-zorro-antd/upload';
import { AdsService, Ad } from './ads.service';
import { HttpClientModule } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzImageModule } from 'ng-zorro-antd/image';
import { EthiopianDatepickerComponent } from '../../../shared/components/ethiopian-datepicker/ethiopian-datepicker.component';

@Component({
  selector: 'app-ads',
  standalone: true,
  imports: [
    CommonModule,
    SharedModule,
    NzTableModule,
    NzButtonModule,
    NzModalModule,
    FormsModule,
    ReactiveFormsModule,
    NzFormModule,
    NzInputModule,
    NzIconModule,
    NzUploadModule,
    HttpClientModule,
    NzCardModule,
    NzImageModule,
    EthiopianDatepickerComponent,
  ],
  templateUrl: './ads.component.html',
  styleUrls: ['./ads.component.scss'],
  providers: [AdsService],
})
export default class AdsComponent implements OnInit {
  ads: Ad[] = [];
  isModalVisible = false;
  modalTitle = '';
  adForm: FormGroup;
  currentAdId: number | null = null;
  fileList: NzUploadFile[] = [];
  public env = environment;
  currentPage = 1;
  pageSize = 9;
  totalItems = 0;
  expandedDescriptions: Set<number> = new Set();
  descriptionLimit = 100;
  isEvent = false; // Track whether user is creating an ad or event

  constructor(
    private fb: FormBuilder,
    private modalService: NzModalService,
    private adsService: AdsService
  ) {
    this.adForm = this.fb.group({
      title: ['', [Validators.required]],
      description: ['', [Validators.required]],
      image: [null],
      expireAt: [null, [Validators.required]], // For ads: expiration date, For events: not used
      startDate: [null], // For events: start date
      endDate: [null], // For events: end date
    });
  }

  ngOnInit(): void {
    this.loadAds();
  }

  loadAds(): void {
    this.adsService.getAds(this.currentPage, this.pageSize).subscribe((response:any) => {
      this.ads = response.data;
      this.totalItems = response.pagination?.totalItems || 0;
    });
  }

  onPageChange(page: number): void {
    this.currentPage = page;
    this.loadAds();
  }

  onPageSizeChange(size: number): void {
    this.pageSize = size;
    this.loadAds();
  }

  showModal(ad?: Ad): void {
    if (ad) {
      this.modalTitle = 'Edit Ad';
      this.currentAdId = ad.id;
      this.adForm.patchValue(ad);
     
      // TODO: Set isEvent based on the ad data when we have that field in the backend
    } else {
      this.modalTitle = 'Create Ad';
      this.currentAdId = null;
      this.adForm.reset();
      this.isEvent = false; // Default to advertisement
      this.fileList = [];
    }
    this.isModalVisible = true;
    this.updateFormValidation(); // Set validation based on content type
  }

  handleOk(): void {
    if (this.adForm.valid) {

      const expireAt = this.adForm.get('expireAt')?.value;
      const formData = new FormData();
      formData.append('title', this.adForm.get('title')?.value);
      formData.append('description', this.adForm.get('description')?.value);
      formData.append('expireAt', expireAt.gregorian);


      if (this.fileList.length > 0) {
        formData.append('image', this.fileList[0] as any);
      }
      if(!this.isEvent){
      if (this.currentAdId) {
        this.adsService.updateAd(this.currentAdId, formData).subscribe(() => {
          this.loadAds();
          this.isModalVisible = false;
          this.fileList = [];
        });
      } else {
        this.adsService.createAd(formData).subscribe(() => {
          this.loadAds();
          this.isModalVisible = false;
          this.fileList = [];
        });
      }
    }
    } else {
      Object.values(this.adForm.controls).forEach((control) => {
        if (control.invalid) {
          control.markAsDirty();
          control.updateValueAndValidity({ onlySelf: true });
        }
      });
    }
  }

  beforeUpload = (file: NzUploadFile): boolean => {
    this.fileList = [file];
    return false;
  };

  handleCancel(): void {
    this.isModalVisible = false;
    this.fileList = [];
  }

  onDateChange(date: Date): void {
    // Handle date change if needed
    console.log('Ethiopian date selected:', date);
  }

  onContentTypeChange(): void {
    // Update form validation based on content type
    this.updateFormValidation();

    // Reset form when switching between ad and event (only for new items)
    if (!this.currentAdId) {
      this.adForm.reset();
      this.fileList = [];
    }
  }

  private updateFormValidation(): void {
    const expireAtControl = this.adForm.get('expireAt');
    const startDateControl = this.adForm.get('startDate');
    const endDateControl = this.adForm.get('endDate');

    if (this.isEvent) {
      // For events: require start and end dates, make expireAt optional
      expireAtControl?.clearValidators();
      startDateControl?.setValidators([Validators.required]);
      endDateControl?.setValidators([Validators.required]);
    } else {
      // For ads: require expireAt, make start and end dates optional
      expireAtControl?.setValidators([Validators.required]);
      startDateControl?.clearValidators();
      endDateControl?.clearValidators();
    }

    // Update validity
    expireAtControl?.updateValueAndValidity();
    startDateControl?.updateValueAndValidity();
    endDateControl?.updateValueAndValidity();
  }

  formatEthiopianDate(dateInput: string | Date): string {
    try {
      const date = typeof dateInput === 'string' ? new Date(dateInput) : dateInput;
      // For now, return a formatted Gregorian date with Ethiopian context
      // In a full implementation, you would convert to Ethiopian calendar
      const options: Intl.DateTimeFormatOptions = {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      };
      return date.toLocaleDateString('en-US', options) + ' (Gregorian)';
    } catch (error) {
      return typeof dateInput === 'string' ? dateInput : dateInput.toString();
    }
  }

  deleteAd(adId: number): void {
    this.modalService.confirm({
      nzTitle: 'Are you sure you want to delete this ad?',
      nzContent: 'This action cannot be undone.',
      nzOkText: 'Yes',
      nzOkType: 'primary',
      nzOkDanger: true,
      nzOnOk: () => {
        this.adsService.deleteAd(adId).subscribe(() => {
          this.loadAds();
        });
      },
      nzCancelText: 'No',
    });
  }

  getTruncatedDescription(description: string, adId: number): string {
    if (!description) return '';

    if (this.expandedDescriptions.has(adId) || description.length <= this.descriptionLimit) {
      return description;
    }

    return description.substring(0, this.descriptionLimit);
  }

  shouldShowReadMore(description: string, adId: number): boolean {
    return description && description.length > this.descriptionLimit && !this.expandedDescriptions.has(adId);
  }

  shouldShowReadLess(description: string, adId: number): boolean {
    return description && description.length > this.descriptionLimit && this.expandedDescriptions.has(adId);
  }

  toggleDescription(adId: number): void {
    if (this.expandedDescriptions.has(adId)) {
      this.expandedDescriptions.delete(adId);
    } else {
      this.expandedDescriptions.add(adId);
    }
  }
}
